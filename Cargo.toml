[package]
name = "tiny-solver"
version = "0.17.1"
edition = "2021"
authors = ["<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON>. <<EMAIL>>"]
readme = "README.md"
license = "MIT OR Apache-2.0"
description = "Factor graph solver"
homepage = "https://github.com/powei-lin/tiny-solver-rs"
repository = "https://github.com/powei-lin/tiny-solver-rs"
keywords = ["factor-graph", "ceres-solver", "minisam"]
categories = ["data-structures", "science", "mathematics"]
exclude = ["/.github/*", "*.ipynb", "./scripts/*", "examples/*", "tests/"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
faer = "0.22.4"
faer-ext = { version = "0.6.0", features = ["nalgebra"] }
log = "0.4.27"
nalgebra = "0.33.2"
num-dual = "0.11.1"
num-traits = "0.2.19"
numpy = { version = "0.23.0", features = ["nalgebra"], optional = true }
pyo3 = { version = "0.23.3", features = ["abi3", "abi3-py38"], optional = true }
# pyo3-log = { version = "0.9.0", optional = true }
rayon = "1.9.0"
simba = "0.9.0"

[[example]]
name = "m3500_benchmark"
path = "examples/m3500_benchmark.rs"

[[example]]
name = "sphere2500"

[[example]]
name = "parking-garage"

[features]
python = ["num-dual/python", "numpy", "pyo3"]

[dev-dependencies]
env_logger = "0.11.8"
itertools = "0.14.0"
nalgebra = { version = "0.33.2", features = ["rand"] }
plotters = "0.3.6"
rand = "0.9.1"

[profile.dev.package.faer]
opt-level = 3

[lib]
name = "tiny_solver"
