name: Rust

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

env:
  CARGO_TERM_COLOR: always

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Dependencies
      run: sudo apt install pkg-config libfreetype6-dev libfontconfig1-dev
    - name: Format
      run: cargo fmt --check --verbose
    - name: Linting
      run: cargo clippy
    - name: Build
      run: cargo build --verbose
    - name: Run tests
      run: cargo test --verbose
