#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum LinearSolverType {
    #[default]
    Sparse<PERSON>holesky,
    SparseQR,
}

pub trait SparseLinearSolver {
    fn solve(
        &mut self,
        residuals: &faer::Mat<f64>,
        jacobians: &faer::sparse::SparseColMat<usize, f64>,
    ) -> Option<faer::Mat<f64>>;
    fn solve_jtj(
        &mut self,
        jtr: &faer::Mat<f64>,
        jtj: &faer::sparse::SparseColMat<usize, f64>,
    ) -> Option<faer::Mat<f64>>;
}
